import * as THREE from 'three';
import { ThingData, ThreeComponent } from './three.component';
import {
  LineSegments2,
} from 'three/examples/jsm/lines/LineSegments2.js';
import { WireframeGeometry } from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { LineSegmentsGeometry } from 'three/examples/jsm/lines/LineSegmentsGeometry';
// import { WireframeGeometry } from 'three/examples/jsm/geometries/WireframeGeometry.js';

export abstract class AbstractThing {
  protected data: ThingData;
  protected mesh: THREE.Object3D;

  constructor(data: ThingData) {
    this.data = data;
    this.mesh = this.createMesh();
    this.mesh.position.set(data.threenode.pos[0], data.threenode.pos[1], data.threenode.pos[2]);
    this.mesh.name = data.name;
    this.addLabel();
  }

  protected abstract createMesh(): THREE.Object3D;

  protected addLabel() {
    const sprite = ThreeComponent.createTextSprite(this.data.name);
    sprite.position.set(0, 1.2, 0);
    // this.mesh.add(sprite);
  }

  getObject(): THREE.Object3D {
    return this.mesh;
  }
}

export class BoxThing extends AbstractThing {
  private borderMaterial!: LineMaterial;
  private border!: LineSegments2;
  private startTime: number;

  constructor(data: ThingData) {
    super(data);
    this.startTime = performance.now() / 1000;
  }

  protected createMesh(): THREE.Object3D {
    const size = this.data.width  ?? 1;
    const h    = this.data.height ?? 1;
    const d    = this.data.depth  ?? 1;

    // Carico la texture "rock" (fallback silenzioso se sbaglia)
    const loader = new THREE.TextureLoader();
    const rockTex = loader.load(
      '/textures/rock.jpg',
      tex => {
        tex.wrapS = tex.wrapT = THREE.RepeatWrapping;
        tex.repeat.set(1, 1);
      },
      undefined,
      () => { /* fail silently */ }
    );

    // Materiale delle facce
    const faceMaterial = new THREE.MeshStandardMaterial({
      color:        0x0f0f0f,
      roughness:    1.0,
      metalness:    0.0,
      map:          rockTex,
      displacementScale: 0.02,
      emissive:        new THREE.Color(0x002200),
      emissiveIntensity: 0.1
    });

    // Geometria principale
    const geometry = new THREE.BoxGeometry(size, h, d);
    const mesh     = new THREE.Mesh(geometry, faceMaterial);

    // Bordi neon più spessi e luminosi
    const wireframe = new WireframeGeometry(geometry);
    const lineGeom = new LineSegmentsGeometry().fromWireframeGeometry(wireframe);

    this.borderMaterial = new LineMaterial({
      color:        new THREE.Color(0x33ff33),  // neon green bright
      linewidth:    2,                      // spessore in unità mondo
      transparent:  true,
      opacity:      0.8,
      depthWrite:   false,
      depthTest:    false,
      blending:     THREE.AdditiveBlending
    });

    // Imposta risoluzione canvas (aggiornare su resize)
    // this.borderMaterial.resolution.set(
    //   ThreeComponent.renderer.domElement.width,
    //   ThreeComponent.renderer.domElement.height
    // );

    this.border = new LineSegments2(lineGeom, this.borderMaterial);
    this.border.computeLineDistances();
    mesh.add(this.border);

    return mesh;
  }

  /**
   * Deve essere chiamato ogni frame!
   */
  public update(): void {
    const t = performance.now() / 1000 - this.startTime;
    // Pulsazione a 4 Hz tra 0.2 e 1.0 di opacità
    this.borderMaterial.opacity = 0.6 + 0.4 * Math.sin(t * 4.0);
  }
}

export class SphereThing extends AbstractThing {
  protected createMesh(): THREE.Object3D {
    const radius = this.data.radius ?? 0.5;
    const widthSeg = this.data.widthSegments ?? 16;
    const heightSeg = this.data.heightSegments ?? 12;
    const color = this.data.status === 'on' ? 0x00ff00 : 0xff0000;
    const geometry = new THREE.SphereGeometry(radius, widthSeg, heightSeg);
    const material = new THREE.MeshStandardMaterial({ color });
    return new THREE.Mesh(geometry, material);
  }
}

export class TriangleThing extends AbstractThing {
  protected createMesh(): THREE.Object3D {
    const base = this.data.base ?? 1;
    const height = this.data.height ?? 1;
    const color = this.data.status === 'on' ? 0x00ff00 : 0xff0000;

    // Create isosceles triangle in X-Y plane and extrude slightly for visibility
    const shape = new THREE.Shape();
    shape.moveTo(-base / 2, 0);
    shape.lineTo(base / 2, 0);
    shape.lineTo(0, height);
    shape.closePath();

    const extrudeSettings: THREE.ExtrudeGeometryOptions = {
      depth: 0.1,
      bevelEnabled: false
    };
    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
    const material = new THREE.MeshStandardMaterial({ color });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.rotateX(-Math.PI / 2);
    return mesh;
  }
}

export class RectangleThing extends AbstractThing {
  protected createMesh(): THREE.Object3D {
    const w = this.data.width ?? 1;
    const h = this.data.height ?? 0.2;
    const d = this.data.depth ?? 1;
    const color = this.data.status === 'on' ? 0x00ff00 : 0xff0000;
    const geometry = new THREE.BoxGeometry(w, h, d);
    const material = new THREE.MeshStandardMaterial({ color });
    return new THREE.Mesh(geometry, material);
  }
}

export class PipeConnection extends AbstractThing {
  private fromObjectName: string;
  private toObjectName: string;
  private radius: number = 0.00005;
  private segments: number;

  constructor(data: ThingData) {
    super(data);
    this.fromObjectName = (data as any).fromObject || '';
    this.toObjectName = (data as any).toObject || '';
    this.radius = 0.000005;
    this.segments = 1;
  }

  protected createMesh(): THREE.Object3D {
    // Create a placeholder pipe - will be updated when connected to actual objects
    const geometry = new THREE.CylinderGeometry(0.05, 0.05, 1, this.segments);
    const material = new THREE.MeshStandardMaterial({
      color: this.data.status === 'on' ? 0x00ff00 : 0xff0000,
      transparent: true,
      opacity: 0.5
    });
    return new THREE.Mesh(geometry, material);
  }

  /**
   * Updates the pipe to connect two objects in the scene
   * @param fromObject The source object to connect from
   * @param toObject The target object to connect to
   */
  public connectObjects(fromObject: THREE.Object3D, toObject: THREE.Object3D): void {
    const fromPos = new THREE.Vector3();
    const toPos = new THREE.Vector3();

    fromObject.getWorldPosition(fromPos);
    toObject.getWorldPosition(toPos);

    // Calculate distance and direction
    const direction = new THREE.Vector3().subVectors(toPos, fromPos);
    const distance = direction.length();

    // Position pipe at midpoint
    const midpoint = new THREE.Vector3().addVectors(fromPos, toPos).multiplyScalar(0.5);
    this.mesh.position.copy(midpoint);

    // Scale pipe to match distance
    this.mesh.scale.y = distance;

    // Rotate pipe to align with direction
    const axis = new THREE.Vector3(0, 1, 0);
    direction.normalize();
    const quaternion = new THREE.Quaternion().setFromUnitVectors(axis, direction);
    this.mesh.setRotationFromQuaternion(quaternion);
  }

  /**
   * Gets the names of the objects this pipe should connect
   */
  public getConnectionInfo(): { from: string, to: string } {
    return {
      from: this.fromObjectName,
      to: this.toObjectName
    };
  }
}
