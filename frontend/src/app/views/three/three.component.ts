import { Component, OnInit, ElementRef, <PERSON>Child, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import * as THREE from 'three';
import { AbstractThing, BoxThing, RectangleThing, SphereThing, TriangleThing, PipeConnection } from './things';

export interface ThingData {
  type: string;
  name: string;
  status: 'on' | 'off' | string;
  width?: number;
  height?: number;
  depth?: number;
  radius?: number;
  base?: number;
  widthSegments?: number;
  heightSegments?: number;
  // Pipe connection specific properties
  fromObject?: string;
  toObject?: string;
  segments?: number;
  threenode: {
    pos: [number, number, number];
  }
}

@Component({
  selector: 'app-three',
  standalone: true,
  templateUrl: './three.component.html',
  styleUrls: ['./three.component.scss']
})
export class ThreeComponent implements OnInit, OnDestroy {
  @ViewChild('rendererContainer', { static: true }) rendererContainer!: ElementRef;

  private scene!: THREE.Scene;
  private camera!: THREE.OrthographicCamera;
  private renderer!: THREE.WebGLRenderer;
  private animationId!: number;
  private clock = new THREE.Clock();

  private robotPivot!: THREE.Object3D; // spostamento corpo
  private headPivot!: THREE.Object3D;  // rotazione testa (mouse)
  private pipeConnections: PipeConnection[] = []; // Store pipe connections for later processing

  private movementState = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    up: false,
    down: false,
  };

  // per mouse look
  private yaw = 0;   // rotazione orizzontale testa target
  private pitch = 0; // rotazione verticale testa target
  private readonly pitchLimit = Math.PI / 3; // ±60°
  private readonly yawSpeed = 0.0025;
  private readonly pitchSpeed = 0.0025;

  // smoothing facoltativo (disabilitato)
  private currentYaw = 0;
  private currentPitch = 0;
  private readonly smoothingEnabled = false;
  private readonly smoothingFactor = 0.1;

  // drag-based look state
  private isDragging = false;
  private prevMouse: { x: number; y: number } | null = null;

  // raycast point-and-click
  private raycaster = new THREE.Raycaster();
  private pointer = new THREE.Vector2();
  private selectedThingMesh: THREE.Object3D | null = null;
  private highlightOutline: THREE.LineSegments | null = null;

  // camera animation for smooth transitions
  private isAnimatingCamera = false;

  // scena di esempio
  private jsonScene: ThingData[] = [
    {
      type: 'box',
      name: 'Rack-1-Server-A',
      status: 'on',
      threenode: {
        pos: [0, 0.5, 0]
      },
      width: 1,
      height: 1,
      depth: 1
    },
    {
      type: 'sphere',
      name: 'Rack-1-Server-B',
      status: 'off',
      threenode: {
        pos: [4, 0.5, 0]
      },
      radius: 0.5,
      widthSegments: 16,
      heightSegments: 12
    },
    {
      type: 'triangle',
      name: 'Rack-2-Server-A',
      status: 'on',
      threenode: {
        pos: [0, 0.5, -2]
      },
      base: 1.5,
      height: 1
    },
    {
      type: 'rectangle',
      name: 'Rack-2-Server-B',
      status: 'on',
      threenode: {
        pos: [4, 0.5, -2]
      },
      width: 2,
      height: 1,
      depth: 1
    },
    {
      type: 'pipe',
      name: 'Connection-1',
      status: 'on',
      threenode: {
        pos: [0, 0, 0] // This will be overridden by the connection logic
      },
      fromObject: 'Rack-1-Server-A',
      toObject: 'Rack-2-Server-B',
      radius: 0.005,
      segments: 8
    },
    {
      type: 'pipe',
      name: 'Connection-2',
      status: 'off',
      threenode: {
        pos: [0, 0, 0] // This will be overridden by the connection logic
      },
      fromObject: 'Rack-2-Server-A',
      toObject: 'Rack-2-Server-B',
      radius: 0.0008,
      segments: 12
    }
  ];

  ngOnInit() {
    this.initThree();
    this.loadJsonScene({ things: this.jsonScene });
    this.animate();
  }

  ngOnDestroy() {
    cancelAnimationFrame(this.animationId);
    this.renderer.dispose();

    // rimozione listener
    window.removeEventListener('keydown', this.onKeyDown);
    window.removeEventListener('keyup', this.onKeyUp);
    window.removeEventListener('resize', this.onResize);
    this.renderer.domElement.removeEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.removeEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.removeEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.removeEventListener('click', this.onClick);
    this.renderer.domElement.removeEventListener('dragstart', this.preventDrag);
  }

  private initThree() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a); // Dark background to match the mood

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.2;
    this.rendererContainer.nativeElement.appendChild(this.renderer.domElement);

    // Camera & pivots - Isometric setup
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera = new THREE.OrthographicCamera(
      (frustumSize * aspect) / -2,
      (frustumSize * aspect) / 2,
      frustumSize / 2,
      frustumSize / -2,
      0.1,
      1000
    );

    this.robotPivot = new THREE.Object3D();
    this.scene.add(this.robotPivot);

    this.headPivot = new THREE.Object3D();
    this.robotPivot.add(this.headPivot);

    // Posizionamento camera per vista isometrica - looking at origin (0,0,0)
    this.camera.position.set(10, 10, 10);
    this.headPivot.add(this.camera);

    // Position robot pivot at origin and make camera look at it
    this.robotPivot.position.set(0, 0, 0);
    this.camera.lookAt(0, 0, 0);

    // Set initial camera angle for isometric view
    this.pitch = 0  ; // -45 degrees looking down
    this.yaw = 0 ; // 45 degrees rotation for isometric angle
    this.currentPitch = this.pitch;
    this.currentYaw = this.yaw;

    // Apply initial rotation
    this.updateCameraRotation();

    // Luci
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3); // Darker ambient for mood
    this.scene.add(ambientLight);
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
    dirLight.position.set(5, 10, 7.5);
    dirLight.castShadow = true;
    dirLight.shadow.mapSize.width = 2048;
    dirLight.shadow.mapSize.height = 2048;
    this.scene.add(dirLight);

    // Add neon green accent light
    const neonLight = new THREE.PointLight(0x00ff00, 10, 50);
    neonLight.position.set(0, 5  , 1);
    this.scene.add(neonLight);

    // Ground plane - glossy dark with reflections
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x111111, // Very dark
      metalness: 0.1,
      roughness: 0.05, // Very smooth for glossy effect
      reflectivity: 0.9,
      clearcoat: 1.0,
      clearcoatRoughness: 0.1,
      transparent: false
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
    ground.position.y = -0.01; // Slightly below y=0 to avoid z-fighting with grid
    ground.receiveShadow = true;
    this.scene.add(ground);

    // Add neon green acid pools
    // this.addAcidPools();

    // Assi e griglia
    // this.scene.add(new THREE.AxesHelper(5));
    this.scene.add(new THREE.GridHelper(20, 20));

    // Input
    this.setupKeyboardMovement();
    this.setupMouseLook();
    this.setupPointerSelection();

    // Resize
    window.addEventListener('resize', this.onResize);
  }

  // resize handler separato per rimozione pulita
  private onResize = () => {
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera.left = (frustumSize * aspect) / -2;
    this.camera.right = (frustumSize * aspect) / 2;
    this.camera.top = frustumSize / 2;
    this.camera.bottom = frustumSize / -2;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
  };

  private onKeyDown = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = true;
        break;
      case 's':
        this.movementState.backward = true;
        break;
      case 'a':
        this.movementState.left = true;  // strafe left
        break;
      case 'd':
        this.movementState.right = true; // strafe right
        break;
      case 'q':
        this.movementState.up = true;    // sale in altezza
        break;
      case 'e':
        this.movementState.down = true;  // scende in altezza
        break;
    }
  };

  private onKeyUp = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = false;
        break;
      case 's':
        this.movementState.backward = false;
        break;
      case 'a':
        this.movementState.left = false;
        break;
      case 'd':
        this.movementState.right = false;
        break;
      case 'q':
        this.movementState.up = false;
        break;
      case 'e':
        this.movementState.down = false;
        break;
    }
  };

  private setupKeyboardMovement() {
    window.addEventListener('keydown', this.onKeyDown);
    window.addEventListener('keyup', this.onKeyUp);
  }

  private setupMouseLook() {
    this.renderer.domElement.addEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.addEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.addEventListener('dragstart', this.preventDrag);
  }

  private setupPointerSelection() {
    this.renderer.domElement.addEventListener('click', this.onClick);
  }

  private preventDrag = (e: Event) => e.preventDefault();

  // ---- DRAG-BASED MOUSE LOOK ----

  private onMouseDown = (e: MouseEvent) => {
    if (e.button !== 0 || this.isAnimatingCamera) return; // solo sinistro e non durante animazione
    this.isDragging = true;
    this.prevMouse = { x: e.clientX, y: e.clientY };
  };

  private onMouseUp = (e: MouseEvent) => {
    if (e.button !== 0) return;
    this.isDragging = false;
    this.prevMouse = null;
  };

  private onMouseMove = (e: MouseEvent) => {
    if (!this.isDragging || !this.prevMouse || this.isAnimatingCamera) return;

    const deltaX = e.clientX - this.prevMouse.x;
    // const deltaY = e.clientY - this.prevMouse.y; // Disabled vertical rotation

    // Only update if there's horizontal movement
    if (Math.abs(deltaX) > 0) {
      // Update only yaw (horizontal rotation) - no pitch (vertical rotation)
      this.yaw -= deltaX * this.yawSpeed;
      // this.pitch remains unchanged - no vertical rotation from mouse

      // Apply rotation to robot pivot (which contains the camera)
      this.robotPivot.rotation.y = this.yaw;
      // headPivot.rotation.x stays at the fixed pitch value
    }

    this.prevMouse = { x: e.clientX, y: e.clientY };
  };

  private updateCameraRotation() {
    // Apply yaw rotation to the robot pivot (horizontal rotation around Y axis)
    this.robotPivot.rotation.y = this.yaw;

    // Keep pitch fixed at the initial isometric angle (no vertical rotation from mouse)
    this.headPivot.rotation.x = this.pitch; // Use the stored pitch value
  }

  // ---- POINT-AND-CLICK SELECTION ----

  private onClick = (e: MouseEvent) => {
    
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.pointer.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
    this.pointer.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

    this.raycaster.setFromCamera(this.pointer, this.camera);

    // Filter out non-selectable objects from raycast targets
    const selectableObjects = this.scene.children.filter(child =>
      child !== this.highlightOutline &&
      !(child instanceof THREE.Sprite) &&
      !(child instanceof THREE.GridHelper) &&
      !(child instanceof THREE.AxesHelper) &&
      !(child instanceof THREE.DirectionalLight) &&
      !(child instanceof THREE.AmbientLight)
    );
    
    const intersects = this.raycaster.intersectObjects(selectableObjects, true);

    
    if (intersects.length > 0) {
      const hit = intersects[0].object;

      // Deselect precedente
      this.clearHighlight();

      // Selezione corrente
      this.selectedThingMesh = hit;

      // Evidenzia con outline wireframe box
      const box = new THREE.Box3().setFromObject(hit);
      const size = new THREE.Vector3();
      const center = new THREE.Vector3();
      box.getSize(size);
      box.getCenter(center);

      // Store selected object position for rotation pivot

      // Initialize orbit camera from current position
      // this.initializeOrbitCamera();

      const geometry = new THREE.BoxGeometry(
        Math.max(size.x, 0.001) * 1.05,
        Math.max(size.y, 0.001) * 1.05,
        Math.max(size.z, 0.001) * 1.05
      );
      const edges = new THREE.EdgesGeometry(geometry);
      const outline = new THREE.LineSegments(
        edges,
        new THREE.LineBasicMaterial({ color: 0xff0000 })
      );
      outline.position.copy(center);
      this.highlightOutline = outline;
      this.scene.add(outline);

      // Mostra nome come sprite sopra (se presente)
      if (hit.name) {
        const sprite = ThreeComponent.createTextSprite(hit.name);
        sprite.position.copy(center);
        sprite.position.y += size.y + 0.2;
        this.scene.add(sprite);
        setTimeout(() => this.scene.remove(sprite), 2500);
      }

      console.log('Clicked on thing:', hit.name || hit.uuid);

      // Don't animate camera when we're now rotating around the object
      // The rotation system will handle positioning automatically
    } else {
      this.clearHighlight();
      this.selectedThingMesh = null;
      this.selectedObjectPosition = null;
    }
  };

  private clearHighlight() {
    if (this.highlightOutline) {
      this.scene.remove(this.highlightOutline);
      this.highlightOutline.geometry.dispose();
      (this.highlightOutline.material as THREE.Material).dispose();
      this.highlightOutline = null;
    }
    this.selectedObjectPosition = null;
  }





private applyKeyboardMovement(delta: number) {
  const moveSpeed = 5 * delta;

  // Ottieni direzione "forward" della camera, proiettata sul piano XZ (y = 0)
  const camDir = new THREE.Vector3();
  this.camera.getWorldDirection(camDir); // include pitch
  camDir.y = 0;
  camDir.normalize();

  // Direzione right come ortogonale in XZ
  const camRight = new THREE.Vector3();
  camRight.crossVectors(camDir, new THREE.Vector3(0, 1, 0)).normalize();

  if (this.movementState.forward) {
    this.robotPivot.position.addScaledVector(camDir, moveSpeed);
  }
  if (this.movementState.backward) {
    this.robotPivot.position.addScaledVector(camDir, -moveSpeed);
  }
  if (this.movementState.left) {
    this.robotPivot.position.addScaledVector(camRight, -moveSpeed);
  }
  if (this.movementState.right) {
    this.robotPivot.position.addScaledVector(camRight, moveSpeed);
  }
  if (this.movementState.up) {
    this.robotPivot.position.y += moveSpeed;
  }
  if (this.movementState.down) {
    this.robotPivot.position.y -= moveSpeed;
  }
}

  private animate = () => {
    this.animationId = requestAnimationFrame(this.animate);
    const delta = this.clock.getDelta(); // delta reale
    const elapsedTime = this.clock.getElapsedTime();

    // Handle camera animation
    this.updateCameraAnimation();

    // Only apply keyboard movement if not animating camera
    if (!this.isAnimatingCamera) {
      this.applyKeyboardMovement(delta);
    }

    // Update pipe connections every frame in case objects have moved
    this.updatePipeConnections();

    this.renderer.render(this.scene, this.camera);
  };

  private updateCameraAnimation() {
    return;
   
  }



  private loadJsonScene(data: { things: ThingData[] }) {
    // Clear previous pipe connections
    this.pipeConnections = [];

    // First pass: create all objects
    data.things.forEach((obj) => {
      let thing: AbstractThing | null = null;
      switch (obj.type.toLowerCase()) {
        case 'box':
          thing = new BoxThing(obj);
          break;
        case 'sphere':
          thing = new SphereThing(obj);
          break;
        case 'triangle':
          thing = new TriangleThing(obj);
          break;
        case 'rectangle':
          thing = new RectangleThing(obj);
          break;
        case 'pipe':
          thing = new PipeConnection(obj);
          this.pipeConnections.push(thing as PipeConnection);
          break;
        default:
          console.warn(`Unknown thing type: ${obj.type}`);
      }
      if (thing) {
        const mesh = thing.getObject();
        mesh.position.set(obj.threenode.pos[0], obj.threenode.pos[1], obj.threenode.pos[2]);
        mesh.name = obj.name;
        mesh.userData['thingData'] = obj;
        mesh.receiveShadow = true;
        mesh.castShadow = true; // Enable shadow casting for objects
        this.scene.add(mesh);
      }
    });

    // Second pass: connect pipes to their target objects
    this.updatePipeConnections();
  }

  private updatePipeConnections() {
    this.pipeConnections.forEach(pipe => {
      const connectionInfo = pipe.getConnectionInfo();
      const fromObject = this.scene.getObjectByName(connectionInfo.from);
      const toObject = this.scene.getObjectByName(connectionInfo.to);

      if (fromObject && toObject) {
        pipe.connectObjects(fromObject, toObject);
      } else {
        console.warn(`Could not connect pipe ${pipe.getObject().name}: ` +
          `fromObject "${connectionInfo.from}" ${fromObject ? 'found' : 'not found'}, ` +
          `toObject "${connectionInfo.to}" ${toObject ? 'found' : 'not found'}`);
      }
    });
  }

  static createTextSprite(text: string): THREE.Sprite {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;
    const context = canvas.getContext('2d')!;
    context.font = '48px Arial';
    context.fillStyle = 'black';
    context.fillText(text, 10, 64);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(3, 1, 1);
    return sprite;
  }
}
